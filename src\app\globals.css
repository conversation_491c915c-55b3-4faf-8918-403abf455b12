@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* BecomingAlpha inspired dark theme - Light mode fallback */
    --background: 220 15% 8%; /* Very dark navy background */
    --foreground: 210 20% 95%; /* Light text */

    --card: 220 15% 12%; /* Dark cards with slight blue tint */
    --card-foreground: 210 20% 92%;

    --popover: 220 15% 10%;
    --popover-foreground: 210 20% 92%;

    --primary: 260 85% 65%; /* Purple-blue primary */
    --primary-foreground: 220 100% 98%;

    --secondary: 220 15% 18%; /* Dark secondary */
    --secondary-foreground: 210 20% 85%;

    --muted: 220 15% 15%;
    --muted-foreground: 210 15% 65%;

    --accent: 280 90% 70%; /* Bright purple accent */
    --accent-foreground: 220 100% 10%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 15% 25%;
    --input: 220 15% 16%;
    --ring: 260 85% 65%;

    --chart-1: 260 85% 65%;
    --chart-2: 280 90% 70%;
    --chart-3: 240 80% 60%;
    --chart-4: 200 85% 65%;
    --chart-5: 320 75% 65%;

    --radius: 1rem; /* Larger radius for modern look */

    /* Sidebar specific theme variables */
    --sidebar-background: 220 15% 6%;
    --sidebar-foreground: 210 20% 88%;
    --sidebar-primary: 260 85% 65%;
    --sidebar-primary-foreground: 220 100% 98%;
    --sidebar-accent: 220 15% 12%;
    --sidebar-accent-foreground: 210 20% 92%;
    --sidebar-border: 220 15% 20%;
    --sidebar-ring: 260 85% 65%;
  }

  .dark {
    /* BecomingAlpha inspired dark theme - Enhanced dark mode */
    --background: 220 15% 4%; /* Even darker background */
    --foreground: 210 20% 96%;

    --card: 220 15% 8%; /* Darker cards */
    --card-foreground: 210 20% 94%;

    --popover: 220 15% 6%;
    --popover-foreground: 210 20% 94%;

    --primary: 260 90% 70%; /* Brighter purple-blue for dark mode */
    --primary-foreground: 220 100% 98%;

    --secondary: 220 15% 12%;
    --secondary-foreground: 210 20% 88%;

    --muted: 220 15% 10%;
    --muted-foreground: 210 15% 70%;

    --accent: 280 95% 75%; /* Brighter purple accent for dark mode */
    --accent-foreground: 220 100% 8%;

    --destructive: 0 75% 65%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 15% 20%;
    --input: 220 15% 12%;
    --ring: 260 90% 70%;

    --chart-1: 260 90% 70%;
    --chart-2: 280 95% 75%;
    --chart-3: 240 85% 65%;
    --chart-4: 200 90% 70%;
    --chart-5: 320 80% 70%;

    /* Dark Sidebar specific theme variables */
    --sidebar-background: 220 15% 2%;
    --sidebar-foreground: 210 20% 90%;
    --sidebar-primary: 260 90% 70%;
    --sidebar-primary-foreground: 220 100% 98%;
    --sidebar-accent: 220 15% 8%;
    --sidebar-accent-foreground: 210 20% 94%;
    --sidebar-border: 220 15% 15%;
    --sidebar-ring: 260 90% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* BecomingAlpha inspired gradient backgrounds */
  .bg-hero-gradient {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(220 15% 12%) 50%, hsl(260 20% 15%) 100%);
  }

  .bg-primary-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  }

  .bg-card-gradient {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(220 15% 14%) 100%);
  }

  /* 3D floating elements */
  .floating-element {
    @apply absolute rounded-full opacity-20;
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    filter: blur(1px);
    animation: float 6s ease-in-out infinite;
  }

  .floating-element-lg {
    @apply w-64 h-64 md:w-80 md:h-80;
  }

  .floating-element-md {
    @apply w-48 h-48 md:w-56 md:h-56;
  }

  .floating-element-sm {
    @apply w-32 h-32 md:w-40 md:h-40;
  }

  /* Mobile-first responsive utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .text-responsive {
    @apply text-sm sm:text-base;
  }

  .heading-responsive {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .card-responsive {
    @apply p-3 sm:p-4 md:p-6;
  }

  .button-responsive {
    @apply px-3 py-2 sm:px-4 sm:py-2.5 text-sm sm:text-base;
  }

  .grid-responsive {
    @apply grid gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  .space-responsive {
    @apply space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8;
  }

  /* Mobile table improvements */
  .table-mobile {
    @apply text-xs sm:text-sm;
  }

  .table-mobile th {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  .table-mobile td {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  /* Mobile form improvements */
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    @apply text-sm sm:text-base;
  }

  /* Mobile sidebar improvements */
  .sidebar-mobile {
    @apply w-64 sm:w-72;
  }
}

/* Remove sidebar gap - Fix layout spacing */
[data-sidebar="sidebar"] + * {
  margin-left: 0 !important;
}

/* Remove any default spacing from SidebarInset */
[data-sidebar="inset"] {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure no gap between sidebar and content */
.sidebar-provider {
  gap: 0 !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Floating animation for 3D elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

/* Subtle pulse animation for elements */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}
