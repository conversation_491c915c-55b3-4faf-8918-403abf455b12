
"use client"; // Needs to be client for Supabase auth check

// Force dynamic rendering to prevent build-time errors
export const dynamic = 'force-dynamic'

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { CheckCircle, Users, Code, Activity, School, Database, BarChart3, Zap, ShieldCheck, Settings2, ArrowRight, LogIn, Briefcase } from 'lucide-react';

import { SyntharaLogo } from '@/components/icons/SyntharaLogo';
import { Footer } from '@/components/layout/Footer';
import React, { useEffect, useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import type { User as SupabaseUser } from '@supabase/supabase-js';

const features = [
  { name: 'Intelligent Data Generation', icon: Database, description: 'Create realistic synthetic datasets tailored to your needs using advanced AI models.' },
  { name: 'In-depth Data Analysis', icon: Bar<PERSON>hart3, description: 'Automatically analyze generated data for quality, insights, and potential issues.' },
  { name: 'Seamless ML Integration', icon: Zap, description: 'Train, evaluate, and deploy machine learning models directly within the platform.' },
  { name: 'Intuitive User Experience', icon: Settings2, description: 'A clean, modern interface designed for ease of use and efficient workflows.' },
  { name: 'Robust Security', icon: ShieldCheck, description: 'Your data and models are protected with industry-standard security practices.' },
  { name: 'Developer Friendly API', icon: Code, description: 'Integrate Synthara into your existing workflows with our powerful and easy-to-use API.' },
];

const targetAudiences = [
  { name: 'Data Scientists', icon: Users, description: 'Accelerate research and model development with high-quality synthetic data.' },
  { name: 'Developers & Testers', icon: Code, description: 'Easily integrate synthetic data generation into your applications and testing pipelines.' },
  { name: 'Business Analysts', icon: Activity, description: 'Explore scenarios and gain insights without compromising real sensitive data.' },
  { name: 'Educators & Students', icon: School, description: 'Access diverse and safe datasets for learning and experimentation in data science.' },
];

const useCases = [
  { title: 'Software Testing', items: ['Generate diverse test data', 'Cover edge cases effectively', 'Reduce reliance on production data'] },
  { title: 'AI Model Training', items: ['Augment limited datasets', 'Create balanced datasets', 'Improve model robustness'] },
  { title: 'Data Privacy Compliance', items: ['Anonymize sensitive information', 'Share data safely', 'Meet GDPR, CCPA requirements'] },
  { title: 'Product Demonstrations', items: ['Showcase features with realistic data', 'Protect customer privacy', 'Create compelling demos'] },
];

const teamMembers = [
  { name: 'Harsha M', role: 'Team Lead', college: 'AIML, Govt. Eng. College Challakere', imageHint: 'person student coding' },
  { name: 'Maruti Gore', role: 'Developer', college: 'AIML, Govt. Eng. College Challakere', imageHint: 'person student tech' },
  { name: 'Manogna', role: 'Researcher', college: 'AIML, Govt. Eng. College Challakere', imageHint: 'person student thinking' },
  { name: 'Sumanth Prasad TM', role: 'Designer', college: 'AIML, Govt. Eng. College Challakere', imageHint: 'person student creative' },
];


export default function HomePage() {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Only initialize Supabase client on the client side
    const supabase = createSupabaseBrowserClient();

    // If Supabase client is not available (e.g., during build), skip auth
    if (!supabase) {
      setLoading(false);
      return;
    }

    async function getUser() {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.warn('[HomePage] Auth check failed:', error);
      } finally {
        setLoading(false);
      }
    }
    getUser();

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        setLoading(false);
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);


  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <header className="py-4 sm:py-6 sticky top-0 bg-background/95 backdrop-blur-xl z-50 border-b border-border/20">
        <nav className="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <Link href="/" aria-label="Synthara AI Homepage" className="flex-shrink-0">
            <SyntharaLogo className="h-8 sm:h-9 lg:h-10 w-auto" />
          </Link>

          {/* Desktop Navigation - Centered */}
          <div className="hidden md:flex items-center space-x-8">
             <Button variant="ghost" asChild className="text-foreground/80 hover:text-foreground">
              <Link href="#features">Products</Link>
            </Button>
            <Button variant="ghost" asChild className="text-foreground/80 hover:text-foreground">
              <Link href="#team">About</Link>
            </Button>
             <Button variant="ghost" asChild className="text-foreground/80 hover:text-foreground">
              <Link href="/help">Affiliate</Link>
            </Button>
            <Button variant="ghost" asChild className="text-foreground/80 hover:text-foreground">
              <Link href="/help">Contact</Link>
            </Button>
            <Button variant="ghost" asChild className="text-foreground/80 hover:text-foreground">
              <Link href="/help">Support</Link>
            </Button>
          </div>

          {/* CTA Button */}
          <div className="flex items-center">
            {loading ? (
                <Button size="lg" disabled className="bg-primary-gradient text-white px-6 py-2.5 rounded-full">Loading...</Button>
            ) : user ? (
                <Button size="lg" asChild className="bg-primary-gradient hover:opacity-90 text-white px-6 py-2.5 rounded-full transition-all">
                    <Link href="/dashboard">Go to Dashboard</Link>
                </Button>
            ) : (
                <Button size="lg" asChild className="bg-primary-gradient hover:opacity-90 text-white px-6 py-2.5 rounded-full transition-all">
                    <Link href="/auth">Get started</Link>
                </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden items-center">
            {loading ? (
                <Button size="sm" disabled className="bg-primary-gradient text-white px-4 py-2 rounded-full">Loading...</Button>
            ) : user ? (
                <Button size="sm" asChild className="bg-primary-gradient hover:opacity-90 text-white px-4 py-2 rounded-full transition-all">
                    <Link href="/dashboard">Dashboard</Link>
                </Button>
            ) : (
                <Button size="sm" asChild className="bg-primary-gradient hover:opacity-90 text-white px-4 py-2 rounded-full transition-all">
                    <Link href="/auth">Get started</Link>
                </Button>
            )}
          </div>
        </nav>
      </header>

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative py-20 sm:py-24 md:py-32 lg:py-40 text-center overflow-hidden bg-hero-gradient">
          {/* Floating 3D Elements */}
          <div className="floating-element floating-element-lg top-20 -left-32 animate-float" style={{animationDelay: '0s'}}></div>
          <div className="floating-element floating-element-md top-40 -right-24 animate-float" style={{animationDelay: '2s'}}></div>
          <div className="floating-element floating-element-sm bottom-32 left-1/4 animate-float" style={{animationDelay: '4s'}}></div>
          <div className="floating-element floating-element-md bottom-20 right-1/3 animate-float" style={{animationDelay: '1s'}}></div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-card/50 backdrop-blur-sm border border-border/50 mb-8">
              <span className="text-sm text-muted-foreground">Elevating Synthetic Data Investment – No Scams, No Rug Pulls, Just Alpha.</span>
            </div>

            <h1 className="font-headline text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-8 text-foreground leading-tight">
              Elevating Synthetic Data<br />
              <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Investment Standards</span>
            </h1>

            <p className="text-lg sm:text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
              For investors, founders/developers, and entrepreneurs<br />
              who are frustrated by scams and uncertainty in crypto
            </p>

            <div className="flex justify-center mb-16">
              <Button size="lg" asChild className="bg-primary-gradient hover:opacity-90 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all shadow-2xl">
                <Link href="/auth">
                  Join Waitlist <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            {/* Floating Elements Showcase */}
            <div className="relative max-w-6xl mx-auto">
              <div className="grid grid-cols-2 gap-8 opacity-30">
                <div className="floating-element w-32 h-32 md:w-48 md:h-48 animate-pulse-glow" style={{animationDelay: '0s'}}></div>
                <div className="floating-element w-24 h-24 md:w-36 md:h-36 animate-pulse-glow" style={{animationDelay: '1.5s'}}></div>
              </div>
            </div>
          </div>
        </section>

        {/* Purpose & Vision Block */}
        <section className="py-20 md:py-32 bg-background relative">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16 md:mb-20">
              <h2 className="font-headline text-4xl sm:text-5xl md:text-6xl font-bold mb-8 text-foreground">
                Our Purpose & Vision
              </h2>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                We believe in a future where data is abundant, accessible, and privacy-preserving. Synthara AI is built to democratize data-driven innovation by providing powerful, intuitive tools for synthetic data.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
              {[
                { title: 'Accelerate Innovation', description: 'Break data bottlenecks and fuel faster development cycles.', icon: Zap },
                { title: 'Enhance Privacy', description: 'Protect sensitive information while enabling data exploration.', icon: ShieldCheck },
                { title: 'Empower Decisions', description: 'Make data-informed choices with robust, customizable datasets.', icon: BarChart3 },
              ].map(benefit => (
                <Card key={benefit.title} className="bg-card-gradient border-border/20 hover:border-primary/20 transition-all duration-300 p-8">
                  <CardHeader className="items-center text-center pb-6">
                    <div className="p-6 bg-primary/10 rounded-2xl mb-6 inline-block">
                        <benefit.icon className="w-12 h-12 text-primary" />
                    </div>
                    <CardTitle className="font-headline text-2xl xl:text-3xl text-foreground">{benefit.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground text-lg leading-relaxed">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Key Features Overview */}
        <section id="features" className="py-20 md:py-32 bg-card/20 relative">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="font-headline text-4xl sm:text-5xl md:text-6xl font-bold text-center mb-16 md:mb-20 text-foreground">
              Core Platform Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {features.map((feature) => (
                <Card key={feature.name} className="bg-card-gradient border-border/20 hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-2 p-6">
                  <CardHeader className="flex flex-col items-center text-center pb-6">
                    <div className="p-4 bg-primary/10 rounded-xl mb-4">
                        <feature.icon className="w-10 h-10 text-primary" />
                    </div>
                    <CardTitle className="font-headline text-xl xl:text-2xl mb-3 text-foreground">{feature.name}</CardTitle>
                    <CardDescription className="text-muted-foreground leading-relaxed text-base">{feature.description}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Target Audience Section */}
        <section className="py-16 md:py-24 bg-background">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="font-headline text-3xl sm:text-4xl md:text-5xl font-semibold text-center mb-12 md:mb-16 text-foreground">
              Built For Innovators Like You
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
              {targetAudiences.map((audience) => (
                <Card key={audience.name} className="text-center shadow-xl hover:shadow-heavy-lg transition-shadow bg-card p-3 sm:p-4">
                  <CardHeader className="items-center pb-3 sm:pb-6">
                     <div className="p-3 sm:p-4 bg-accent/10 rounded-full mb-3 sm:mb-4 inline-block">
                        <audience.icon className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-accent" />
                    </div>
                    <CardTitle className="font-headline text-lg sm:text-xl">{audience.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs sm:text-sm text-muted-foreground">{audience.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Use Cases Section */}
        <section className="py-16 md:py-24 bg-secondary/50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="font-headline text-3xl sm:text-4xl md:text-5xl font-semibold text-center mb-12 md:mb-16 text-foreground">
              Versatile Use Cases
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {useCases.map((useCase) => (
                <Card key={useCase.title} className="shadow-xl hover:shadow-heavy-lg transition-shadow bg-card">
                  <CardHeader className="p-6">
                    <CardTitle className="font-headline text-xl text-primary">{useCase.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 pt-0">
                    <ul className="space-y-3">
                      {useCase.items.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-500 mr-2.5 mt-0.5 shrink-0" />
                          <span className="text-muted-foreground text-sm">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section id="team" className="py-16 md:py-24 bg-background">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-4">
              <h3 className="text-lg sm:text-xl font-semibold text-primary tracking-wider">AIML</h3>
              <h4 className="text-sm sm:text-base text-muted-foreground">Government Engineering College Challakere</h4>
            </div>
            <h2 className="font-headline text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-center mb-8 sm:mb-12 md:mb-16 text-foreground">
              Meet the Team
            </h2>
            {/* Mobile: 2x2 Grid, Tablet: 2x2, Desktop: 4x1 */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
              {teamMembers.map((member) => (
                <Card key={member.name} className="text-center shadow-xl hover:shadow-heavy-lg transition-shadow bg-card overflow-hidden">
                  {/* Medium Circle Image Holder */}
                  <div className="p-4 sm:p-6 flex justify-center">
                    <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 rounded-full bg-gradient-to-br from-primary/10 to-accent/10 flex items-center justify-center border-2 border-primary/20">
                      <div className="text-center text-muted-foreground">
                        <div className="text-2xl sm:text-3xl md:text-4xl">👤</div>
                      </div>
                    </div>
                  </div>
                  <CardHeader className="p-3 sm:p-4 lg:p-6 pt-0">
                    <CardTitle className="font-headline text-sm sm:text-base lg:text-xl">{member.name}</CardTitle>
                    <CardDescription className="text-primary text-xs sm:text-sm">{member.role}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </section>

         {/* Call to Action Section */}
        <section className="py-24 md:py-40 text-center bg-primary-gradient relative overflow-hidden">
          {/* Background floating elements */}
          <div className="floating-element floating-element-lg top-10 -left-20 animate-float opacity-10" style={{animationDelay: '0s'}}></div>
          <div className="floating-element floating-element-md bottom-10 -right-16 animate-float opacity-10" style={{animationDelay: '3s'}}></div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <h2 className="font-headline text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-8 text-white">
              Ready to Revolutionize Your Data Strategy?
            </h2>
            <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed">
              Join thousands of innovators building the future with Synthara AI. Get started today and experience the next generation of data.
            </p>
             {loading ? (
                 <Button size="lg" disabled className="bg-white text-primary px-8 py-4 rounded-full text-lg font-semibold">Loading...</Button>
            ) : user ? (
                 <Button size="lg" asChild className="bg-white hover:bg-white/90 text-primary px-8 py-4 rounded-full text-lg font-semibold transition-all shadow-2xl">
                    <Link href="/dashboard">Access Your Dashboard</Link>
                </Button>
            ) : (
                <Button size="lg" asChild className="bg-white hover:bg-white/90 text-primary px-8 py-4 rounded-full text-lg font-semibold transition-all shadow-2xl">
                    <Link href="/auth">Get Started for Free</Link>
                </Button>
            )}
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}

